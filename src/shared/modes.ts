import * as vscode from "vscode"

import {
	type GroupOptions,
	type GroupEntry,
	type ModeConfig,
	type CustomModePrompts,
	type ExperimentId,
	type ToolGroup,
	type PromptComponent,
	DEFAULT_MODES,
} from "@roo-code/types"

import { addCustomInstructions } from "../core/prompts/sections/custom-instructions"
import { TOOL_GROUPS, ALWAYS_AVAILABLE_TOOLS } from "./tools"

import { EXPERIMENT_IDS } from "./experiments"

// 导入sast指令
const SAST_INSTRUCTIONS = `
### **Phase 0: Initialization Check – Resume or Start New**

**Before starting any actions to the \`.docx\` SAST report in Phase 1:**

1. Use tool get_pending_vulnerability_json_files(if failed to use the tool, then check if the MCP server sast-fixer-mcp existing in mcp settings json, if not, then SET UP THE SAST FIXER MCP SERVER) get all file paths in the .scanissuefix directory that end with '_new.json' (indicating pending vulnerabilities), check if the \`.scanissuefix\` directory exist at the root of the project and detect files with suffix \`_new.json\` which under \`.scanissuefix\` directory

2. **If the \`.scanissuefix\` directory does not exist or contains no files with suffix \`_new.json\`**:

   * Send a message:

   > **No unfinished SAST fix tasks detected. Starting a new session.**

   Then clear (or create) the \`.scanissuefix\` directory and automatically proceed to **Phase 1**.

3. **If one or more \`_new.json\` files are found under \`.scanissuefix\` directory:**

   * Send a message:

   > **Unfinished SAST vulnerability fix tasks detected.**
   > Please reply with one of the following options:
   >
   > * **resume**: Continue your previous session.
   > * **new**: Start a brand new session (this will clear existing data).

   Wait for the user's reply. Based on their response:

   * **resume** → Skip Phase 1 and automatically proceed to **Phase 2**.
   * **new** → Clear \`.scanissuefix\` contents and proceed to **Phase 1**.

---

### **Phase 1: Parse SAST Report (DOCX → JSON)**

1. REMOVE the \`.scanissuefix\` directory (if it exists).

2. Open the \`.docx\` SAST report using the \`MCP-Doc\` tool and parse it into structured JSON and save to \`_new.json\` files.

**When Phase 1 completes**, send a message:

> **Phase 1 complete: Parsed X vulnerabilities and created JSON files.**

Then proceed automatically to **Phase 2**.



### **Phase 2: Vulnerability Triage & Fix**

**1. Prompt User to Choose Fix Approach:**

First, prompt the user to choose how they want to handle the vulnerability fixes:

> **Before continuing, please choose how you want to handle fixes:**
>
> * **Automatic Fix**: Automatically apply all fixes without review.
> * **Review Code Diff and Fix**: Review the code diff and False Positive (FP) probability for each fix manually before applying.

**2. Iterate Through All JSON Files:**
The system will then iterate through each JSON file located in the \`.scanissuefix\` directory that ends with \`_new.json\`. For each file:

   **3. Iterate Through Each Issue in the \`code_list\`:**
   For every JSON file, read the original data in the JSON file, the system will go through the \`code_list\`, which contains the vulnerabilities to be fixed. For each vulnerability, use the information in \`issue_title\`, \`issue_desc\`, \`fix_advice\`, \`code_location\`, \`code_line_num\` and \`code_details\`, Instead of using just the surrounding lines near \`code_location\`, the system will now read 200 lines before and after the code_line_num for each vulnerability (code_location) to perform analysis and determine fixes，it will do the following:

   * **If User Chooses "Review Code Diff"**:
      * **Display Code Diff and False Positive Probability**: For each vulnerability, the system must show the user a code diff between the original code and the proposed fixed code. It will also show the False Positive (FP) probability, which is the likelihood that the identified issue might not actually be a real vulnerability.

      Example(**must show the diff to the user in each vulnerability**):
      > **Review Code Diff for Vulnerability: SQL Injection in Function X**
      > **False Positive Probability**: 30%
      \`\`\`diff
      public class Hello1
      {
         public static void Main()
         {
      -      System.Console.WriteLine("Hello, World!");
      +      System.Console.WriteLine("Rock all night long!");
         }
      }
      \`\`\`

      * **User Decision**: After reviewing the code diff, the user can choose from the following options:
         * **Apply Fix**: The system applies the fix with apply_diff tool to the code， and updates the \`_new.json\` JSON file with apply_diff tool as well by marking the status as \`"fixed"\`. It also updates any false positive data if applicable. Example:
            \`\`\`json
               "code_list": [
               {
               "code_details": "", # code_details field might not be the exact location of the vulnerability but rather additional code context, which requires understanding within the broader code context.
               "code_location": "", 
               "code_line_num": "", # The code_line_num from the report may be inaccurate. Read the surrounding code to find the precise location of the vulnerability. 
               "status": "fixed|ignored|false_positive",
               "false_positive_probability": 20,
               "false_positive_reason": ""
               },
            \`\`\`
         * **Ignore Fix**: The system marks the issue as \`"ignored"\` and does not apply the fix.

         * **Mark as False Positive**: If the user determines that the issue is a false positive, the system will mark it as \`"false_positive"\`. It will also include a reason for why it is considered a false positive in the \`false_positive_reason\` field.

   * **If User Chooses "Automatic Fix"**:

      * **Automatically Apply Fixes**: The system applies the fix with apply_diff tool for the identified vulnerability automatically. It updates the JSON file with apply_diff tool as well by setting the status to \`"fixed"\` and adds any false positive information (if available). Example:
            \`\`\`json
               "code_list": [
               {
               "code_details": "", # code_details field might not be the exact location of the vulnerability but rather additional code context, which requires understanding within the broader code context.
               "code_location": "",
               "code_line_num": "", # The code_line_num from the report may be inaccurate. Read the surrounding code to find the precise location of the vulnerability. 
               "status": "fixed|ignored|false_positive",
               "false_positive_probability": 20,
               "false_positive_reason": ""
               },
            \`\`\`

      * **Proceed to Next Code Location**: After applying the fix, the system proceeds to the next vulnerability in the list or moves to the next JSON file once all issues in the current JSON file are addressed.

   **4. Rename \`_new.json\` to \`_finished.json\`:**
   After completing all fixes (either manually or automatically) for the vulnerabilities in a given JSON file, the system will rename the file from \`_new.json\` to \`_finished.json\` to indicate that the task for this specific file has been completed.
   * **Rename Action**: The \`_new.json\` file will be renamed to \`_finished.json\`.

**5. Complete Fixes for All Vulnerabilities:**
* The system will continue applying fixes (whether automatically or after reviewing diffs) until all vulnerabilities across all the JSON files are resolved.

Once all vulnerabilities are addressed and the corresponding JSON files are renamed \`_new.json\` to \`_finished.json\`, the system will proceed to **Phase 3**.

---

### **Phase 3: Final Report Generation (CSV)**

1. **Generate CSV Report:**

   * check if all the json files fixed from \`_new.json\` to \`_finished.json\` with tool get_pending_vulnerability_json_files, then use tool generate_csv_report generate a final CSV report (sast_fix_report.csv) in '.scanissuefix', from all files ending with '_finished.json' in the '.scanissuefix' directory.
   * The CSV report will contain the following columns: issue_title, issue_level, code_location, code_line_num, code_details, status, false_positive_probability, false_positive_reason.


**Note:**
* Throughout the entire workflow, **all user-facing messages must be in Chinese** to ensure clear communication with the user.
* System instructions and logic can remain in English internally.


====

SET UP THE SAST FIXER MCP SERVER

Set up the MCP server from README while adhering to these MCP server installation rules:
- Start by loading the MCP documentation.
- Use "sast-fixer-mcp" as the server name in mcp settings json.
- Make sure you read the user's existing mcp settings json file before editing it with this new mcp, to not overwrite any existing servers.
- Use commands aligned with the user's shell and operating system best practices.
- The following README may contain instructions that conflict with the user's OS, in which case proceed thoughtfully.
- Once installed, demonstrate the server's capabilities by using one of its tools.
Here is the project's README to help you get started:

# SAST_FIXER MCP Server

A Model Context Protocol (MCP) server that provides dedicated handling of Static Application Security Testing (SAST) reports. It includes capabilities for parsing DOCX reports, tracking vulnerability remediation statuses, and exporting comprehensive fix reports. Seamless integration with Zhanlu AI Programmer enables automated remediation for vulnerabilities identified in SAST processes.

### Available Tools


* **convert_sast_docx_to_json** - Converts SAST reports from DOCX format to JSON.

  * \`file_path\` (string, required): Path to the SAST report DOCX file.

* **get_pending_vulnerability_json_files** - Retrieves all pending vulnerability JSON files (\`_new.json\`) from the \`.scanissuefix\` directory.

* **generate_csv_report** - Generates a CSV report from all resolved vulnerability JSON files (\`_finished.json\`).

## Installation

### Prerequisites

* **Python 3.10 or higher** is required. Verify your Python version:

\`\`\`bash
python --version  # or python3 --version
\`\`\`

* Check if you have the appropriate Python environment installed:

\`\`\`bash
which python  # or which python3
\`\`\`


### Install Python

If you do not have Python installed or your version is not match the prerequisites, it's recommended to install the most stable version of Python 3.12 using the following direct download links:

* **Mac**: [Download Python 3.12 for macOS](https://www.python.org/ftp/python/3.12.10/python-3.12.10-macos11.pkg)

  * For silent installation on macOS:

  \`\`\`bash
  sudo installer -pkg /path/to/python-3.12.4-macos11.pkg -target /
  \`\`\`

* **Windows**: [Download Python 3.12 for Windows](https://www.python.org/ftp/python/3.12.9/python-3.12.9-amd64.exe)

  * For silent installation on Windows, run:

  \`\`\`bash
  python-3.12.4-amd64.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0
  \`\`\`


### Upgrade Python

If your current Python version does not meet the required standard, download and install the recommended Python 3.12 version using the above links. Ensure your system's PATH points to the new Python installation.

### Using PIP

download [sast_fixer_mcp-0.1.2-py3-none-any.whl](https://sast-mcp-server.eos-huhehaote-1.cmecloud.cn/sast_fixer_mcp-0.1.2-py3-none-any.whl?versionId=null&response-content-disposition=attachment&AWSAccessKeyId=DQW127WHCZXKU1QD76BL&Expires=1783740030&Signature=rrhYgnO9FJvk3rxlyp0nA1lNClw%3D), 
you can install \`SAST_FIXER_MCP\` via pip:

\`\`\`bash
pip install -U sast_fixer_mcp-0.1.2-py3-none-any.whl
\`\`\`

## Configuration

### Configure for VS Code

Add to your VS Code mcp_settings.json file:

For manual installation, add the following JSON block to your User Settings (JSON) file in VS Code. You can do this by pressing \`Ctrl + Shift + P\` and typing \`Preferences: Open User Settings (JSON)\`.

Optionally, you can add it to a file called \`.vscode/mcp.json\` in your workspace. This will allow you to share the configuration with others.

> Note that the \`mcp\` key is needed when using the \`mcp.json\` file.


<details>
<summary>Using pip installation</summary>
In certain Conda environments or when environment variables and IDE-specific Python interceptors differ, specifying "command": "python" might be incorrect and cause issues such as "No module named sast_fixer_mcp" or "MCP error -32000: Connection closed". To prevent such problems, use which python to determine the accurate Python executable path and update the configuration accordingly.

For example:

\`\`\`bash
which python
/opt/miniconda3/envs/agent_py312/bin/python
\`\`\`

\`\`\`json
{
  "mcpServers": {
    "sast-fixer-mcp": {
      "command": "python",
      "args": ["-m", "sast_fixer_mcp"],
      "alwaysAllow": [
        "convert_sast_docx_to_json",
        "get_pending_vulnerability_json_files",
        "generate_csv_report"
      ],
      "timeout": 1800,
      "disabled": false
    }
  }
}
\`\`\`
</details>

====

`

export type Mode = string

// Helper to extract group name regardless of format
export function getGroupName(group: GroupEntry): ToolGroup {
	if (typeof group === "string") {
		return group
	}

	return group[0]
}

// Helper to get group options if they exist
function getGroupOptions(group: GroupEntry): GroupOptions | undefined {
	return Array.isArray(group) ? group[1] : undefined
}

// Helper to check if a file path matches a regex pattern
export function doesFileMatchRegex(filePath: string, pattern: string): boolean {
	try {
		const regex = new RegExp(pattern)
		return regex.test(filePath)
	} catch (error) {
		console.error(`Invalid regex pattern: ${pattern}`, error)
		return false
	}
}

// Helper to get all tools for a mode
export function getToolsForMode(groups: readonly GroupEntry[]): string[] {
	const tools = new Set<string>()

	// Add tools from each group
	groups.forEach((group) => {
		const groupName = getGroupName(group)
		const groupConfig = TOOL_GROUPS[groupName]
		groupConfig.tools.forEach((tool: string) => tools.add(tool))
	})

	// Always add required tools
	ALWAYS_AVAILABLE_TOOLS.forEach((tool) => tools.add(tool))

	return Array.from(tools)
}


type ModesModelNotSupportMap = {
	[key: string]: string[]
}

// models can't used in some mode
export const modesModleNotSuportList: ModesModelNotSupportMap = {
	architect: ["zhanlu-r1", "jiutian-75b"],
	code: ["zhanlu-r1", "jiutian-75b"],
	"algorithm-practice": ["zhanlu-r1", "jiutian-75b"],
	simple: [], // zhanlu-r1模型能力不足，仅在智能问答模式下使用
	test: ["zhanlu-r1", "jiutian-75b"],
	"project-fix": ["zhanlu-r1", "jiutian-75b"],
	sast: ["zhanlu-r1", "jiutian-75b"],
	"code-review": ["zhanlu-r1", "jiutian-75b"],
	readme: ["zhanlu-r1", "jiutian-75b"],
}

// Main modes configuration as an ordered array
export const modes = DEFAULT_MODES

// Export the default mode slug
export const defaultModeSlug = modes[1].slug

// Helper function to check if a mode is a built-in mode
export function isBuiltInMode(modeSlug: string): boolean {
	return modes.some((mode) => mode.slug === modeSlug)
}

// Helper function to get the appropriate mode slug for service_type
// Returns the actual modeSlug if it's a built-in mode, otherwise returns "customer"
export function getServiceTypeModeSlug(modeSlug?: string): string {
	// this.options.modeSlug ? `ai_developer_${getServiceTypeModeSlug(this.options.modeSlug)}` : "ai_developer",
	if (!modeSlug) {
		return ""
	}
	const currentMode = isBuiltInMode(modeSlug) ? modeSlug : "customer"
	return `ai_developer_${currentMode}`
}

// Helper functions
export function getModeBySlug(slug: string, customModes?: ModeConfig[]): ModeConfig | undefined {
	// Check custom modes first
	const customMode = customModes?.find((mode) => mode.slug === slug)
	if (customMode) {
		return customMode
	}
	// Then check built-in modes
	return modes.find((mode) => mode.slug === slug)
}

export function getModeConfig(slug: string, customModes?: ModeConfig[]): ModeConfig {
	const mode = getModeBySlug(slug, customModes)
	if (!mode) {
		throw new Error(`No mode found for slug: ${slug}`)
	}
	return mode
}

// Get all available modes, with custom modes overriding built-in modes
export function getAllModes(customModes?: ModeConfig[]): ModeConfig[] {
	if (!customModes?.length) {
		return [...modes]
	}

	// Start with built-in modes
	const allModes = [...modes]

	// Process custom modes
	customModes.forEach((customMode) => {
		const index = allModes.findIndex((mode) => mode.slug === customMode.slug)
		if (index !== -1) {
			// Override existing mode
			allModes[index] = customMode
		} else {
			// Add new mode
			allModes.push(customMode)
		}
	})

	return allModes
}

// Check if a mode is custom or an override
export function isCustomMode(slug: string, customModes?: ModeConfig[]): boolean {
	return !!customModes?.some((mode) => mode.slug === slug)
}

/**
 * Find a mode by its slug, don't fall back to built-in modes
 */
export function findModeBySlug(slug: string, modes: readonly ModeConfig[] | undefined): ModeConfig | undefined {
	return modes?.find((mode) => mode.slug === slug)
}

/**
 * Get the mode selection based on the provided mode slug, prompt component, and custom modes.
 * If a custom mode is found, it takes precedence over the built-in modes.
 * If no custom mode is found, the built-in mode is used with partial merging from promptComponent.
 * If neither is found, the default mode is used.
 */
export function getModeSelection(mode: string, promptComponent?: PromptComponent, customModes?: ModeConfig[]) {
	const customMode = findModeBySlug(mode, customModes)
	const builtInMode = findModeBySlug(mode, modes)

	// If we have a custom mode, use it entirely
	if (customMode) {
		return {
			roleDefinition: customMode.roleDefinition || "",
			baseInstructions: customMode.customInstructions || "",
			description: customMode.description || "",
		}
	}

	// Otherwise, use built-in mode as base and merge with promptComponent
	const baseMode = builtInMode || modes[0] // fallback to default mode

	return {
		roleDefinition: promptComponent?.roleDefinition || baseMode.roleDefinition || "",
		baseInstructions: promptComponent?.customInstructions || baseMode.customInstructions || "",
		description: baseMode.description || "",
	}
}

// Edit operation parameters that indicate an actual edit operation
const EDIT_OPERATION_PARAMS = ["diff", "content", "operations", "search", "replace", "args", "line"] as const

// Custom error class for file restrictions
export class FileRestrictionError extends Error {
	constructor(mode: string, pattern: string, description: string | undefined, filePath: string, tool?: string) {
		const toolInfo = tool ? `Tool '${tool}' in mode '${mode}'` : `This mode (${mode})`
		super(
			`${toolInfo} can only edit files matching pattern: ${pattern}${description ? ` (${description})` : ""}. Got: ${filePath}`,
		)
		this.name = "FileRestrictionError"
	}
}

export function isToolAllowedForMode(
	tool: string,
	modeSlug: string,
	customModes: ModeConfig[],
	toolRequirements?: Record<string, boolean>,
	toolParams?: Record<string, any>, // All tool parameters
	experiments?: Record<string, boolean>,
): boolean {
	// Always allow these tools
	if (ALWAYS_AVAILABLE_TOOLS.includes(tool as any)) {
		return true
	}
	if (experiments && Object.values(EXPERIMENT_IDS).includes(tool as ExperimentId)) {
		if (!experiments[tool]) {
			return false
		}
	}

	// Check tool requirements if any exist
	if (toolRequirements && typeof toolRequirements === "object") {
		if (tool in toolRequirements && !toolRequirements[tool]) {
			return false
		}
	} else if (toolRequirements === false) {
		// If toolRequirements is a boolean false, all tools are disabled
		return false
	}

	const mode = getModeBySlug(modeSlug, customModes)
	if (!mode) {
		return false
	}

	// Check if tool is in any of the mode's groups and respects any group options
	for (const group of mode.groups) {
		const groupName = getGroupName(group)
		const options = getGroupOptions(group)

		const groupConfig = TOOL_GROUPS[groupName]

		// If the tool isn't in this group's tools, continue to next group
		if (!groupConfig.tools.includes(tool)) {
			continue
		}

		// If there are no options, allow the tool
		if (!options) {
			return true
		}

		// For the edit group, check file regex if specified
		if (groupName === "edit" && options.fileRegex) {
			const filePath = toolParams?.path
			// Check if this is an actual edit operation (not just path-only for streaming)
			const isEditOperation = EDIT_OPERATION_PARAMS.some((param) => toolParams?.[param])

			// Handle single file path validation
			if (filePath && isEditOperation && !doesFileMatchRegex(filePath, options.fileRegex)) {
				throw new FileRestrictionError(mode.name, options.fileRegex, options.description, filePath, tool)
			}

			// Handle XML args parameter (used by MULTI_FILE_APPLY_DIFF experiment)
			if (toolParams?.args && typeof toolParams.args === "string") {
				// Extract file paths from XML args with improved validation
				try {
					const filePathMatches = toolParams.args.match(/<path>([^<]+)<\/path>/g)
					if (filePathMatches) {
						for (const match of filePathMatches) {
							// More robust path extraction with validation
							const pathMatch = match.match(/<path>([^<]+)<\/path>/)
							if (pathMatch && pathMatch[1]) {
								const extractedPath = pathMatch[1].trim()
								// Validate that the path is not empty and doesn't contain invalid characters
								if (extractedPath && !extractedPath.includes("<") && !extractedPath.includes(">")) {
									if (!doesFileMatchRegex(extractedPath, options.fileRegex)) {
										throw new FileRestrictionError(
											mode.name,
											options.fileRegex,
											options.description,
											extractedPath,
											tool,
										)
									}
								}
							}
						}
					}
				} catch (error) {
					// Re-throw FileRestrictionError as it's an expected validation error
					if (error instanceof FileRestrictionError) {
						throw error
					}
					// If XML parsing fails, log the error but don't block the operation
					console.warn(`Failed to parse XML args for file restriction validation: ${error}`)
				}
			}
		}

		return true
	}

	return false
}

// Create the mode-specific default prompts
export const defaultPrompts: Readonly<CustomModePrompts> = Object.freeze(
	Object.fromEntries(
		modes.map((mode) => [
			mode.slug,
			{
				roleDefinition: mode.roleDefinition,
				whenToUse: mode.whenToUse,
				customInstructions: mode.customInstructions,
				description: mode.description,
			},
		]),
	),
)

// Helper function to get all modes with their prompt overrides from extension state
export async function getAllModesWithPrompts(context: vscode.ExtensionContext): Promise<ModeConfig[]> {
	const customModes = (await context.globalState.get<ModeConfig[]>("customModes")) || []
	const customModePrompts = (await context.globalState.get<CustomModePrompts>("customModePrompts")) || {}

	const allModes = getAllModes(customModes)
	return allModes.map((mode) => ({
		...mode,
		roleDefinition: customModePrompts[mode.slug]?.roleDefinition ?? mode.roleDefinition,
		whenToUse: customModePrompts[mode.slug]?.whenToUse ?? mode.whenToUse,
		customInstructions: customModePrompts[mode.slug]?.customInstructions ?? mode.customInstructions,
		// description is not overridable via customModePrompts, so we keep the original
	}))
}

// Helper function to get complete mode details with all overrides
export async function getFullModeDetails(
	modeSlug: string,
	customModes?: ModeConfig[],
	customModePrompts?: CustomModePrompts,
	options?: {
		cwd?: string
		globalCustomInstructions?: string
		language?: string
	},
): Promise<ModeConfig> {
	// First get the base mode config from custom modes or built-in modes
	const baseMode = getModeBySlug(modeSlug, customModes) || modes.find((m) => m.slug === modeSlug) || modes[0]

	// Check for any prompt component overrides
	const promptComponent = customModePrompts?.[modeSlug]

	// Get the base custom instructions
	const baseCustomInstructions = promptComponent?.customInstructions || baseMode.customInstructions || ""
	const baseWhenToUse = promptComponent?.whenToUse || baseMode.whenToUse || ""
	const baseDescription = promptComponent?.description || baseMode.description || ""

	// If we have cwd, load and combine all custom instructions
	let fullCustomInstructions = baseCustomInstructions
	if (options?.cwd) {
		fullCustomInstructions = await addCustomInstructions(
			baseCustomInstructions,
			options.globalCustomInstructions || "",
			options.cwd,
			modeSlug,
			{ language: options.language },
		)
	}

	// Return mode with any overrides applied
	return {
		...baseMode,
		roleDefinition: promptComponent?.roleDefinition || baseMode.roleDefinition,
		whenToUse: baseWhenToUse,
		description: baseDescription,
		customInstructions: fullCustomInstructions,
	}
}

// Helper function to safely get role definition
export function getRoleDefinition(modeSlug: string, customModes?: ModeConfig[]): string {
	const mode = getModeBySlug(modeSlug, customModes)
	if (!mode) {
		console.warn(`No mode found for slug: ${modeSlug}`)
		return ""
	}
	return mode.roleDefinition
}

// Helper function to safely get description
export function getDescription(modeSlug: string, customModes?: ModeConfig[]): string {
	const mode = getModeBySlug(modeSlug, customModes)
	if (!mode) {
		console.warn(`No mode found for slug: ${modeSlug}`)
		return ""
	}
	return mode.description ?? ""
}

// Helper function to safely get whenToUse
export function getWhenToUse(modeSlug: string, customModes?: ModeConfig[]): string {
	const mode = getModeBySlug(modeSlug, customModes)
	if (!mode) {
		console.warn(`No mode found for slug: ${modeSlug}`)
		return ""
	}
	return mode.whenToUse ?? ""
}

// Helper function to safely get custom instructions
export function getCustomInstructions(modeSlug: string, customModes?: ModeConfig[]): string {
	const mode = getModeBySlug(modeSlug, customModes)
	if (!mode) {
		console.warn(`No mode found for slug: ${modeSlug}`)
		return ""
	}
	return mode.customInstructions ?? ""
}
