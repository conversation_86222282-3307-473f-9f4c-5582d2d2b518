// Support prompts
type PromptParams = Record<string, string | any[]>

const generateDiagnosticText = (diagnostics?: any[]) => {
	if (!diagnostics?.length) return ""
	return `\nCurrent problems detected:\n${diagnostics
		.map((d) => `- [${d.source || "Error"}] ${d.message}${d.code ? ` (${d.code})` : ""}`)
		.join("\n")}`
}

export const createPrompt = (template: string, params: PromptParams): string => {
	return template.replace(/\${(.*?)}/g, (_, key) => {
		if (key === "diagnosticText") {
			return generateDiagnosticText(params["diagnostics"] as any[])
			// eslint-disable-next-line no-prototype-builtins
		} else if (params.hasOwnProperty(key)) {
			// Ensure the value is treated as a string for replacement
			const value = params[key]
			if (typeof value === "string") {
				return value
			} else {
				// Convert non-string values to string for replacement
				return String(value)
			}
		} else {
			// If the placeholder key is not in params, replace with empty string
			return ""
		}
	})
}

interface SupportPromptConfig {
	template: string
}

type SupportPromptType =
	| "ENHANCE"
	| "CONDENSE"
	| "EXPLAIN"
	| "FIX"
	| "IMPROVE"
	| "ADD_TO_CONTEXT"
	| "TERMINAL_ADD_TO_CONTEXT"
	| "TERMINAL_FIX"
	| "TERMINAL_EXPLAIN"
	| "NEW_TASK"
	| "UNIT_TEST"
	| "CODE_REVIEW"
	| "COMMENT_CODE"

const supportPromptConfigs: Record<SupportPromptType, SupportPromptConfig> = {
	ENHANCE: {
		template: `生成这个提示的增强版本（仅回复增强后的提示 - 不要包含对话、解释、开头引导、项目符号、占位符或引号）：

\${userInput}`,
	},
	CONDENSE: {
		template: `Your task is to create a detailed summary of the conversation so far, paying close attention to the user's explicit requests and your previous actions.
This summary should be thorough in capturing technical details, code patterns, and architectural decisions that would be essential for continuing with the conversation and supporting any continuing tasks.

Your summary should be structured as follows:
Context: The context to continue the conversation with. If applicable based on the current task, this should include:
  1. Previous Conversation: High level details about what was discussed throughout the entire conversation with the user. This should be written to allow someone to be able to follow the general overarching conversation flow.
  2. Current Work: Describe in detail what was being worked on prior to this request to summarize the conversation. Pay special attention to the more recent messages in the conversation.
  3. Key Technical Concepts: List all important technical concepts, technologies, coding conventions, and frameworks discussed, which might be relevant for continuing with this work.
  4. Relevant Files and Code: If applicable, enumerate specific files and code sections examined, modified, or created for the task continuation. Pay special attention to the most recent messages and changes.
  5. Problem Solving: Document problems solved thus far and any ongoing troubleshooting efforts.
  6. Pending Tasks and Next Steps: Outline all pending tasks that you have explicitly been asked to work on, as well as list the next steps you will take for all outstanding work, if applicable. Include code snippets where they add clarity. For any next steps, include direct quotes from the most recent conversation showing exactly what task you were working on and where you left off. This should be verbatim to ensure there's no information loss in context between tasks.

Example summary structure:
1. Previous Conversation:
  [Detailed description]
2. Current Work:
  [Detailed description]
3. Key Technical Concepts:
  - [Concept 1]
  - [Concept 2]
  - [...]
4. Relevant Files and Code:
  - [File Name 1]
	- [Summary of why this file is important]
	- [Summary of the changes made to this file, if any]
	- [Important Code Snippet]
  - [File Name 2]
	- [Important Code Snippet]
  - [...]
5. Problem Solving:
  [Detailed description]
6. Pending Tasks and Next Steps:
  - [Task 1 details & next steps]
  - [Task 2 details & next steps]
  - [...]

Output only the summary of the conversation so far, without any additional commentary or explanation.`,
	},
	EXPLAIN: {
		template: `
### 技能
1. 你现在可以完美解释多语言的代码，代码解释中提及的变量名和变量值禁止使用引号
2. 并根据代码功能逻辑生成合理的Mermaid流程图（Flowchart）代码

### Mermaid-Flowchart语法规范
1. 基本结构
- 节点：节点是流程图中的基本元素，通常用几何形状表示。
- 边：边是连接节点的箭头或线，表示节点之间的关系。
\`\`\`mermaid
flowchart LR
    A --> B
\`\`\`
2. 节点定义
- 默认节点：节点可以用 \`id\` 表示，\`id\` 会显示在节点中。
- 带文本的节点：可以在节点中显示与 \`id\` 不同的文本。文本内部禁止使用 \`'\`、 \`"\`、 \`()\`、 \`&\`等特殊字符，例如D[计算 ge (msg)]、F[返回 "msg"]都是不合规情况，正确语法为D[计算 ge]、F[返回msg]
- Unicode 文本：使用双引号 \`"\` 包裹 Unicode 文本。
- Markdown 格式化：使用双引号和反引号 \`"\` 包裹 Markdown 文本。
\`\`\`mermaid
flowchart LR
    id["This ❤ Unicode"]
    markdown["\`This is _Markdown_\`"]
\`\`\`
3. 方向
- 流程图方向：可以通过 \`flowchart TD\`（从上到下）或 \`flowchart LR\`（从左到右）来定义流程图的方向。
\`\`\`mermaid
flowchart TD
    Start --> Stop
\`\`\`
4. 节点形状
- 矩形：A[矩形节点]，表示普通步骤。
- 圆角矩形：A(圆角矩形节点)，表示特定步骤。
- 体育场形：A([体育场形节点])，表示特殊步骤。
- 子程序形：A[[子程序节点]]，表示子程序。
- 圆柱形：A[(圆柱形节点)]，表示数据存储。
- 圆形：A((圆形节点))，表示开始或结束点。
- 菱形：A{菱形节点}，表示决策点。
- 六边形：A{{六边形节点}}，表示特定事件。
- 平行四边形：A[/平行四边形节点/]，表示输入操作。
- 反向平行四边形：A[\反向平行四边形节点\]，表示输出操作。
- 梯形：A[/梯形节点\]，表示顺序步骤。
- 反向梯形：A[\反向梯形节点/]，表示反向步骤。
- 不对称矩形：A>不对称矩形节点]，表示条件或筛选步骤。
- 双圈节点：A(((双圈节点)))，表示重要步骤。
\`\`\`mermaid
flowchart LR
    id0[This is the text in the box]
    id1(This is the text in the box)
    id2([This is the text in the box])
    id3[[This is the text in the box]]
    id4[(Database)]
    id5((This is the text in the circle))
    id6{This is the text in the box}
    id7{{This is the text in the box}}
    id8[/This is the text in the box/]
    id9[\This is the text in the box\]
    id10[/Christmas\]
    id11[\Go shopping/]
    id12>This is the text in the box]
    id13(((This is the text in the circle)))
\`\`\`
5. 新形状（Mermaid v11.3.0+）
- 新形状：Mermaid 引入了 30 多种新形状，如 \`card\`、\`hourglass\`、\`bolt\` 等，可以通过 \`@{ shape: shapeName }\` 语法使用。
\`\`\`mermaid
flowchart RL
    A@{ shape: manual-file, label: "File Handling"}
    B@{ shape: manual-input, label: "User Input"}
\`\`\`
6. 特殊字符
- 特殊字符：可以使用双引号包裹特殊字符，或使用 HTML 实体编码。
\`\`\`mermaid
flowchart LR
    id1["This is the (text) in the box"]
    A["A double quote:#quot;"] --> B["A dec char:#9829;"]
\`\`\`
7. Markdown 字符串
- Markdown 字符串：支持在节点、边和子图标签中使用 Markdown 格式化文本。
\`\`\`mermaid
flowchart LR
    a("\`The cat in the hat\`") -- "edge label" --> b{{"\`The dog in the hog\`"}}
\`\`\`
8. 边的类型
- 带箭头的边：使用-->表示带箭头的边，如A-->B。
- 无箭头的边：使用---表示无箭头的边，如A --- B。
- 带文本的边：可在边的定义中添加文本，如A-- This is the text! ---B或A---|This is the text|B。
- 带箭头和文本的边：如A-->|text|B或A-- text -->B。
- 虚线边：使用-.->，如A-.->B。
- 带文本的虚线边：如A-. text .-> B。
- 粗线边：使用==>，如A ==> B。
- 带文本的粗线边：如A == text ==> B。
- 不可见边：使用~~~，如A ~~~ B。
- 链式边：可在一行中声明多个边，如A -- text --> B -- text2 --> C。
- 其他箭头的边：圆圈边，如A --o B；交叉边，如A --x B。
- 多方向箭头的边：如A o--o B、B <--> C、C x--x D。
- 边的最小长度：通过在边定义中添加额外的破折号来指定，如B ---->|No| E。
9. 边的长度
- 链接长度：可以通过添加额外的 \`-\` 或 \`=\` 来调整链接的长度。
\`\`\`mermaid
flowchart TD
    A --> B
    B ----> C
\`\`\`
10. 边的文本格式规范：
- 如果文本中包含特殊字符（如逗号、引号等），确保它们不会被解析器误读。
- 避免使用逗号分隔：如果需要传递多个值，可以将它们合并为一个字符串，例如："value1, value2, value3"。
- 检查特殊字符：如果文本中包含特殊字符（如引号、破折号等），确保它们被正确转义或用引号括起来，如：A -- "文本内容" --> B。
- 简化代码：如果代码较长，尝试逐步简化，直到找到导致错误的具体位置。
11. 子图
- 子图定义：使用 \`subgraph\` 定义子图。
\`\`\`mermaid
flowchart TB
    subgraph one
    a1 --> a2
    end
    subgraph two
    b1 --> b2
    end
\`\`\`
12. 样式和类
- 节点样式：可以使用 \`style\` 为节点定义样式。
- 类定义：可以使用 \`classDef\` 定义类，并将类应用到节点。
\`\`\`mermaid
flowchart LR
    A:::someclass --> B
    classDef someclass fill:#f96
\`\`\`
13. 注释
- 注释：使用 \`%%\` 添加注释，注释内容会被忽略。
\`\`\`mermaid
flowchart LR
    %% this is a comment
    A --> B
\`\`\`
14. 无分号的声明
- 无分号的声明：在声明边时，可以省略分号，并且允许在节点和边之间添加空格。
\`\`\`mermaid
flowchart LR
    A[Hard edge] -->|Link text| B(Round edge)
    B --> C{Decision}
\`\`\`

### 你需要对给定代码进行详细解释并生成符合要求的流程图。请严格遵守以下规范：

一. 必须返回两部分内容：
1. 清晰、有逻辑的代码解释（采用有序列表格式）。
2. 正确、规范的 Mermaid 流程图，必须为代码逻辑建模。

二. Mermaid 流程图规范：
1. 所有节点文本必须使用 双引号（"）包裹。
2. 文本中禁止包含 括号、双引号、函数名禁止带参数。
3. 节点内容应表达清晰、简洁的步骤含义

三. 代码解释要求：
1. 用有序列表逐步展开每段逻辑，避免平铺直叙。
2. 需要包含以下要素(注意：根据代码实际情况决定是否添加)：
若为算法代码，请补充：

核心思想（需以标题形式单独列出）
算法关键点（需以标题形式单独列出）
时间复杂度 与 空间复杂度
代码解析：要输出对整段代码的解析，对每一行的代码都加以注释输出
示例演示：输入样例与对应输出，并解释运行过程

若为非算法代码（如工具类、配置代码）：请聚焦在功能拆解与使用场景解释。不要输出算法复杂度和空间复杂度。

### 输出格式要求（必须严格遵循）
若为算法类代码，请输出以下格式：
### 代码解释

【核心思想】
xxx

1. ...
2. ...
3. ...

【代码解析】
xxx

【算法关键点】
1. ...
2. ...
3. ...

**时间复杂度**：
...

**空间复杂度**：
...

【示例演示】
输入：...
输出：...
说明：...

### 控制流图
\`\`\`mermaid
flowchart TD 
XXX
\`\`\`

若为非算法类代码，请输出以下格式：
### 代码解释

【核心思想】
xxx

1. ...
2. ...
3. ...

### 控制流图
\`\`\`mermaid
flowchart TD 
XXX
\`\`\`

Explain the following code from file path \${filePath}:\${startLine}-\${endLine}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`
`,
	},
	FIX: {
		template: `Fix any issues in the following code from file path \${filePath}:\${startLine}-\${endLine}
\${diagnosticText}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please:
1. Address all detected problems listed above (if any)
2. Identify any other potential bugs or issues
3. Provide corrected code
4. Explain what was fixed and why

Output Requirements:
The response must include problem description and optimization suggestions, optimized code, and explanation, and must follow the format below

### 1. 问题描述

### 2. 优化后的代码

### 3. 解释

`,
	},
	IMPROVE: {
		template: `Improve the following code from file path \${filePath}:\${startLine}-\${endLine}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please suggest improvements for:
1. Code readability and maintainability
2. Performance optimization
3. Best practices and patterns
4. Error handling and edge cases

Provide the improved code along with explanations for each enhancement.`,
	},
	UNIT_TEST: {
		template: `Generate unit tests for the following code from file path \${filePath}:\${startLine}-\${endLine}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please create comprehensive unit tests that:
1. Test the main functionality
2. Cover edge cases and potential error conditions
3. Achieve good code coverage
4. Follow best testing practices for this language/framework
5. Only test core business logic and methods in common service classes.
6. When a method contains multiple conditional branches (such as \`if\`, \`else\`, \`switch\`, etc.), unit tests should cover all branch paths** to ensure each branch behaves as expected.
7. Ensure edge cases and exceptional scenarios are considered.** If a method handles exceptions, null values, or extreme inputs, unit tests should cover all these cases to ensure the program remains stable under all conditions.
8. Do not write tests for private methods, getters and setters, or simple wrapper classes or data objects.
9. Use descriptive test method names** to make the purpose of each test easier to understand, and ensure names follow the conventions of the programming language being used.
10. Keep test code clean and concise, and avoid duplicating the same test logic.
11. Avoid unnecessary package imports.** Even if the input code includes many imports, do not include them in the unit tests unless they are actually used.
Provide complete test code that can be directly used.`,
	},
	ADD_TO_CONTEXT: {
		template: `\${filePath}:\${startLine}-\${endLine}
\`\`\`
\${selectedText}
\`\`\``,
	},
	TERMINAL_ADD_TO_CONTEXT: {
		template: `\${userInput}
Terminal output:
\`\`\`
\${terminalContent}
\`\`\``,
	},
	TERMINAL_FIX: {
		template: `\${userInput}
Fix this terminal command:
\`\`\`
\${terminalContent}
\`\`\`

Please:
1. Identify any issues in the command
2. Provide the corrected command
3. Explain what was fixed and why`,
	},
	TERMINAL_EXPLAIN: {
		template: `\${userInput}
Explain this terminal command:
\`\`\`
\${terminalContent}
\`\`\`

Please provide:
1. What the command does
2. Explanation of each part/flag
3. Expected output and behavior`,
	},
	NEW_TASK: {
		template: `\${userInput}`,
	},
	CODE_REVIEW: {
		template: `\${userInput}`,
	},
	COMMENT_CODE: {
		template: `Add comprehensive comments to the following code from file path \${filePath}:\${startLine}-\${endLine}
\${userInput}

\`\`\`
\${selectedText}
\`\`\`

Please add appropriate comments that:
1. Explain the purpose and functionality of the code
2. Document parameters, return values, and side effects for functions/methods  
3. Clarify complex logic and algorithms
4. Add inline comments for non-obvious code sections
5. Follow the commenting conventions of the programming language
6. Keep comments concise but informative
7. Avoid obvious comments that just repeat what the code does

Provide the complete code with comments added in the appropriate places.`,
	},
} as const

export const supportPrompt = {
	default: Object.fromEntries(Object.entries(supportPromptConfigs).map(([key, config]) => [key, config.template])),
	get: (customSupportPrompts: Record<string, any> | undefined, type: SupportPromptType): string => {
		return customSupportPrompts?.[type] ?? supportPromptConfigs[type].template
	},
	create: (type: SupportPromptType, params: PromptParams, customSupportPrompts?: Record<string, any>): string => {
		const template = supportPrompt.get(customSupportPrompts, type)
		return createPrompt(template, params)
	},
} as const

export type { SupportPromptType }

export type CustomSupportPrompts = {
	[key: string]: string | undefined
}
