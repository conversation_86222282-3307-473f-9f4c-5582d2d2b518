import * as vscode from "vscode"
import * as dotenvx from "@dotenvx/dotenvx"
import * as path from "path"
import { v4 as uuidv4 } from "uuid"

// Load environment variables from .env file
try {
	// Specify path to .env file in the project root directory
	const envPath = path.join(__dirname, "..", ".env")
	dotenvx.config({ path: envPath })
} catch (e) {
	// Silently handle environment loading errors
	console.warn("Failed to load environment variables:", e)
}

import { CloudService } from "@roo-code/cloud"
import { TelemetryService, PostHogTelemetryClient } from "@roo-code/telemetry"

import "./utils/path" // Necessary to have access to String.prototype.toPosix.
import { createOutputChannelLogger, createDualLogger } from "./utils/outputChannelLogger"

import { Package } from "./shared/package"
import { formatLanguage } from "./shared/language"
import { ContextProxy } from "./core/config/ContextProxy"
import { ClineProvider } from "./core/webview/ClineProvider"
import { DIFF_VIEW_URI_SCHEME } from "./integrations/editor/DiffViewProvider"
import { TerminalRegistry } from "./integrations/terminal/TerminalRegistry"
import { McpServerManager } from "./services/mcp/McpServerManager"
import { CodeIndexManager } from "./services/code-index/manager"
import { MdmService } from "./services/mdm/MdmService"
import { migrateSettings } from "./utils/migrateSettings"
import { autoImportSettings } from "./utils/autoImportSettings"
import { ZHANLU_PROFILES, getZhanluProfileNames } from "./utils/zhanluUtils"
import { API } from "./extension/api"

import {
	handleUri,
	registerCommands,
	registerCodeActions,
	registerTerminalActions,
	CodeActionProvider,
} from "./activate"
import { WebviewMessage } from "./shared/WebviewMessage"
import { AiProvider } from "./completion/aiProvider"
import { DomSymbol } from "./cross-file/codelens.util"
import { ActiveEditorTracker } from "./core/ActiveEditorTracker"
import { initializeI18n } from "./i18n"
import { logger } from "./utils/logger"
import { CopyPasteEditProvider } from "./completion/copyPasteEditProvider"

/**
 * Built using https://github.com/microsoft/vscode-webview-ui-toolkit
 *
 * Inspired by:
 *  - https://github.com/microsoft/vscode-webview-ui-toolkit-samples/tree/main/default/weather-webview
 *  - https://github.com/microsoft/vscode-webview-ui-toolkit-samples/tree/main/frameworks/hello-world-react-cra
 */

let outputChannel: vscode.OutputChannel
let extensionContext: vscode.ExtensionContext

// This method is called when your extension is activated.
// Your extension is activated the very first time the command is executed.
export async function activate(context: vscode.ExtensionContext) {
	// Only initialize critical components immediately
	extensionContext = context
	outputChannel = vscode.window.createOutputChannel(Package.outputChannel)
	context.subscriptions.push(outputChannel)
	outputChannel.appendLine(`${Package.name} extension activated - ${JSON.stringify(Package)}`)

	// Migrate old settings to new
	await migrateSettings(context, outputChannel)

	// Initialize telemetry service.
	const telemetryService = TelemetryService.createInstance()

	try {
		telemetryService.register(new PostHogTelemetryClient())
	} catch (error) {
		console.warn("Failed to register PostHogTelemetryClient:", error)
	}

	// Create logger for cloud services
	const cloudLogger = createDualLogger(createOutputChannelLogger(outputChannel))

	// Initialize Roo Code Cloud service.
	await CloudService.createInstance(context, {
		stateChanged: () => ClineProvider.getVisibleInstance()?.postStateToWebview(),
		log: cloudLogger,
	})

	// Initialize MDM service
	const mdmService = await MdmService.createInstance(cloudLogger)

	// Initialize i18n for internationalization support
	initializeI18n(context.globalState.get("language") ?? formatLanguage(vscode.env.language))

	// Initialize global logger
	logger.initialize(outputChannel)
	// Initialize terminal shell execution handlers.
	TerminalRegistry.initialize()

	// Initialize the active editor tracker
	const tracker = ActiveEditorTracker.getInstance()
	context.subscriptions.push({ dispose: () => tracker.dispose() })

	// Get default commands from configuration.
	const defaultCommands = vscode.workspace.getConfiguration(Package.name).get<string[]>("allowedCommands") || []

	// Initialize global state if not already set.
	if (!context.globalState.get("allowedCommands")) {
		context.globalState.update("allowedCommands", defaultCommands)
	}

	const contextProxy = await ContextProxy.getInstance(context)
	const codeIndexManager = CodeIndexManager.getInstance(context)

	try {
		await codeIndexManager?.initialize(contextProxy)
	} catch (error) {
		outputChannel.appendLine(
			`[CodeIndexManager] Error during background CodeIndexManager configuration/indexing: ${error.message || error}`,
		)
	}

	const provider = new ClineProvider(context, outputChannel, "sidebar", contextProxy, codeIndexManager, mdmService)
	TelemetryService.instance.setProvider(provider)

	if (codeIndexManager) {
		context.subscriptions.push(codeIndexManager)
	}

	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider(ClineProvider.sideBarId, provider, {
			webviewOptions: { retainContextWhenHidden: true },
		}),
	)

	// Auto-import configuration if specified in settings
	try {
		await autoImportSettings(outputChannel, {
			providerSettingsManager: provider.providerSettingsManager,
			contextProxy: provider.contextProxy,
			customModesManager: provider.customModesManager,
		})
	} catch (error) {
		outputChannel.appendLine(
			`[AutoImport] Error during auto-import: ${error instanceof Error ? error.message : String(error)}`,
		)
	}

	registerCommands({ context, outputChannel, provider })

	// Synchronize Zhanlu profiles with current supported models
	await performZhanluProfileSync(context, provider, outputChannel)
	/**
	 * We use the text document content provider API to show the left side for diff
	 * view by creating a virtual document for the original content. This makes it
	 * readonly so users know to edit the right side if they want to keep their changes.
	 *
	 * This API allows you to create readonly documents in VSCode from arbitrary
	 * sources, and works by claiming an uri-scheme for which your provider then
	 * returns text contents. The scheme must be provided when registering a
	 * provider and cannot change afterwards.
	 *
	 * Note how the provider doesn't create uris for virtual documents - its role
	 * is to provide contents given such an uri. In return, content providers are
	 * wired into the open document logic so that providers are always considered.
	 *
	 * https://code.visualstudio.com/api/extension-guides/virtual-documents
	 */
	const diffContentProvider = new (class implements vscode.TextDocumentContentProvider {
		provideTextDocumentContent(uri: vscode.Uri): string {
			return Buffer.from(uri.query, "base64").toString("utf-8")
		}
	})()

	context.subscriptions.push(
		vscode.workspace.registerTextDocumentContentProvider(DIFF_VIEW_URI_SCHEME, diffContentProvider),
	)

	context.subscriptions.push(vscode.window.registerUriHandler({ handleUri }))

	context.subscriptions.push(
		vscode.languages.registerDocumentPasteEditProvider({ pattern: "**" }, new CopyPasteEditProvider(context), {
			providedPasteEditKinds: [],
			copyMimeTypes: [],
			pasteMimeTypes: [],
		}),
	)

	// Register code actions provider.
	context.subscriptions.push(
		vscode.languages.registerCodeActionsProvider({ pattern: "**/*" }, new CodeActionProvider(), {
			providedCodeActionKinds: CodeActionProvider.providedCodeActionKinds,
		}),
	)

	registerCodeActions(context)
	registerTerminalActions(context)

	// Add handlers for Zhanlu login verification
	context.subscriptions.push(
		vscode.window.registerWebviewPanelSerializer(ClineProvider.tabPanelId, {
			async deserializeWebviewPanel(_webviewPanel: vscode.WebviewPanel, _state: any) {
				// This is called when a webview panel is restored
				// We don't need to do anything special here
			},
		}),
	)

	// Extend the webview message handler to handle Zhanlu authentication messages
	const originalMessageHandler = provider.resolveWebviewView.bind(provider)
	provider.resolveWebviewView = async function (webviewView) {
		await originalMessageHandler(webviewView)

		// Get the webview instance
		const webview = webviewView.webview

		// 使用通用函数添加Zhanlu消息处理程序
		applyZhanluMessageHandlers(context, provider, webview)
	}
	// 代码智能提示
	const domSymbol = new DomSymbol()
	const aiProvider = new AiProvider(extensionContext, outputChannel, domSymbol)
	// 在后台异步预加载 AiProvider，提升首次使用体验
	aiProvider.preload()
	context.subscriptions.push(vscode.languages.registerInlineCompletionItemProvider({ pattern: "**" }, aiProvider))
	// Allows other extensions to activate once Zhanlu is ready.
	vscode.commands.executeCommand("zhanlu.activationCompleted")

	// Implements the `ZhanluAPI` interface.
	const socketPath = process.env.ZHANLU_IPC_SOCKET_PATH
	const enableLogging = typeof socketPath === "string"

	// Watch the core files and automatically reload the extension host.
	if (process.env.NODE_ENV === "development") {
		const pattern = "**/*.ts"

		const watchPaths = [
			{ path: context.extensionPath, name: "extension" },
			{ path: path.join(context.extensionPath, "../packages/types"), name: "types" },
			{ path: path.join(context.extensionPath, "../packages/telemetry"), name: "telemetry" },
			{ path: path.join(context.extensionPath, "../packages/cloud"), name: "cloud" },
		]

		console.log(
			`♻️♻️♻️ Core auto-reloading is ENABLED. Watching for changes in: ${watchPaths.map(({ name }) => name).join(", ")}`,
		)

		watchPaths.forEach(({ path: watchPath, name }) => {
			const watcher = vscode.workspace.createFileSystemWatcher(new vscode.RelativePattern(watchPath, pattern))

			watcher.onDidChange((uri) => {
				console.log(`♻️ ${name} file changed: ${uri.fsPath}. Reloading host…`)
				vscode.commands.executeCommand("workbench.action.reloadWindow")
			})

			context.subscriptions.push(watcher)
		})
	}

	return new API(outputChannel, provider, socketPath, enableLogging)
}

// This method is called when your extension is deactivated.
export async function deactivate() {
	outputChannel.appendLine(`${Package.name} extension deactivated`)
	await McpServerManager.cleanup(extensionContext)
	TelemetryService.instance.shutdown()
	TerminalRegistry.cleanup()
}

// 定义公共函数，用于应用Zhanlu消息处理程序到webview
const applyZhanluMessageHandlers = (
	context: vscode.ExtensionContext,
	provider: ClineProvider,
	webview: vscode.Webview,
) => {
	context.subscriptions.push(
		webview.onDidReceiveMessage(async (message: WebviewMessage) => {
			// Handle Zhanlu authentication messages
			if (message.type === "getAuthCode") {
				try {
					// Dynamically import authentication components only when needed
					const { getZhanluBaseUrl, plugin_version, RsaPublicKey } = await import("./utils/eop")

					const urlPath: string = `/api/query/acepilot-h5/manager/code/getAuthCode`
					const requestUrl = `${getZhanluBaseUrl()}${urlPath}`
					// Call Zhanlu login API to get verification code
					const response = await fetch(requestUrl, {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							plugin_type: "vscode",
							plugin_version: plugin_version,
							request: uuidv4(),
						},
						body: JSON.stringify({
							telephone: RsaPublicKey(message.data?.telephone?.trim() ?? ""),
							secret: RsaPublicKey(message.data?.secret?.trim() ?? ""),
						}),
					})

					const data = await response.json()

					// Send result back to webview
					await provider.postMessageToWebview({
						type: "commandResponse",
						command: "getAuthCodeResponse",
						success: data.state === "OK",
						errorMessage: data.errorMessage || null,
					})
				} catch (error: any) {
					// Handle error
					await provider.postMessageToWebview({
						type: "commandResponse",
						command: "getAuthCodeResponse",
						success: false,
						errorMessage: error.message || "Failed to get verification code",
					})
				}
			}

			if (message.type === "checkVerificationCode") {
				try {
					// Dynamically import authentication components only when needed
					const { getZhanluBaseUrl, plugin_version, RsaPublicKey, AESDecrypt } = await import("./utils/eop")

					const urlPath: string = "/api/query/acepilot-h5/manager/code/checkCode"
					const requestUrl = `${getZhanluBaseUrl()}${urlPath}`

					// Call Zhanlu verification code API
					const response = await fetch(requestUrl, {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							plugin_type: "vscode",
							// plugin_version: context.extension.packageJSON.version,
							plugin_version: plugin_version,
							request: uuidv4(),
						},
						body: JSON.stringify({
							telephone: RsaPublicKey(message.data?.telephone ?? ""),
							code: message.data?.code,
						}),
					})

					const data = await response.json()

					// If successful, use AES decryption to get credentials
					if (data.body?.result) {
						const aesToken = message.data?.secret || ""
						const credentials = {
							AccessKey: AESDecrypt(data.body.ak, aesToken),
							secretKey: AESDecrypt(data.body.sk, aesToken),
							token: AESDecrypt(data.body.license, aesToken),
						}

						// Send result and credentials back to webview
						await provider.postMessageToWebview({
							type: "commandResponse",
							command: "checkVerificationCodeResponse",
							success: true,
							credentials: credentials,
						})
					} else {
						await provider.postMessageToWebview({
							type: "commandResponse",
							command: "checkVerificationCodeResponse",
							success: false,
							errorMessage: data.errorMessage || "Verification code check failed",
						})
					}
				} catch (error: any) {
					await provider.postMessageToWebview({
						type: "commandResponse",
						command: "checkVerificationCodeResponse",
						success: false,
						errorMessage: error.message || "Verification code check failed",
					})
				}
			}

			if (message.type === "verifyZhanluCredentials") {
				try {
					// Dynamically import authentication components only when needed
					const { buildOpUrl, plugin_version } = await import("./utils/eop")

					// Verify Zhanlu credentials
					const credentials = message.data
					const urlPath: string = "/api/acepilot/zhanlu/v1/engines/login"
					const asl: { [key: string]: string } = {
						AccessKey: credentials?.AccessKey?.trim() ?? "",
						secretKey: credentials?.secretKey?.trim() ?? "",
						token: credentials?.token?.trim() ?? "",
					}

					const requestUrl = buildOpUrl(urlPath, asl)
					const request = new Request(requestUrl, {
						method: "POST",
						headers: {
							"Content-Type": "application/json",
							plugin_type: "vscode",
							plugin_version: plugin_version,
							request: uuidv4(),
						},
						body: JSON.stringify({}),
					})
					// Call Zhanlu login API to verify credentials
					const response = await fetch(request)

					const data = await response.json()

					if (data.state === "OK") {
						await provider.postMessageToWebview({
							type: "commandResponse",
							command: "verifyZhanluCredentialsResponse",
							success: true,
						})
					} else {
						await provider.postMessageToWebview({
							type: "commandResponse",
							command: "verifyZhanluCredentialsResponse",
							success: false,
							errorMessage: data.errorMessage || "Credentials verification failed",
						})
					}
				} catch (error: any) {
					await provider.postMessageToWebview({
						type: "commandResponse",
						command: "verifyZhanluCredentialsResponse",
						success: false,
						errorMessage: error.message || "Credentials verification failed",
					})
				}
			}
		}),
	)
}

/**
 * Universal Zhanlu profile management system
 * Synchronizes profiles with the current supported list and removes deprecated ones
 * Uses globalState to ensure cleanup only runs once per configuration change
 */
async function performZhanluProfileSync(
	context: vscode.ExtensionContext,
	provider: ClineProvider,
	outputChannel: vscode.OutputChannel,
): Promise<void> {
	// Create a version key based on current supported profiles
	// This ensures cleanup runs when the profile list changes
	const currentProfilesHash = JSON.stringify(ZHANLU_PROFILES.map((p) => p.name).sort())
	const SYNC_KEY = `zhanluProfileSync_${Buffer.from(currentProfilesHash).toString("base64").slice(0, 10)}`

	try {
		// Check if sync has already been performed for current profile configuration
		const syncCompleted = context.globalState.get<boolean>(SYNC_KEY, false)
		if (syncCompleted) {
			return // Already synced for current configuration
		}

		outputChannel.appendLine("Performing Zhanlu profile synchronization...")

		// Get existing profiles
		const existingProfiles = await provider.providerSettingsManager.listConfig()

		// Get current supported Zhanlu profile names
		const currentSupportedProfiles = getZhanluProfileNames()

		// Find system-created Zhanlu profiles that are no longer supported
		const deprecatedProfiles = existingProfiles.filter((profile) => {
			// Check if it's a Zhanlu profile (by provider or known legacy names)
			const isZhanluProfile =
				profile.apiProvider === "zhanlu" ||
				profile.name.includes("deepseek") ||
				profile.name.includes("zhanlu") ||
				profile.name.includes("jiutian")

			// Only consider it deprecated if it's a Zhanlu profile but not in current supported list
			return isZhanluProfile && !currentSupportedProfiles.includes(profile.name)
		})

		// Remove deprecated profiles
		let removedCount = 0
		for (const profile of deprecatedProfiles) {
			try {
				await provider.providerSettingsManager.deleteConfig(profile.name)
				outputChannel.appendLine(`Removed deprecated Zhanlu profile: ${profile.name}`)
				removedCount++
			} catch (error) {
				outputChannel.appendLine(`Warning: Failed to remove profile ${profile.name}: ${error}`)
			}
		}

		// Check for missing supported profiles and create them
		const existingProfileNames = existingProfiles.map((p) => p.name)
		const missingProfiles = currentSupportedProfiles.filter((name) => !existingProfileNames.includes(name))

		let createdCount = 0
		if (missingProfiles.length > 0) {
			outputChannel.appendLine(
				`Found ${missingProfiles.length} missing Zhanlu profiles: ${missingProfiles.join(", ")}`,
			)

			// Check if user has existing Zhanlu credentials
			const accessKey = provider.contextProxy.getSecret("zhanluAccessKey")
			const secretKey = provider.contextProxy.getSecret("zhanluSecretKey")
			const token = provider.contextProxy.getSecret("zhanluToken")

			const hasCredentials = !!(
				accessKey &&
				accessKey.trim() !== "" &&
				secretKey &&
				secretKey.trim() !== "" &&
				token &&
				token.trim() !== ""
			)

			// Create missing profiles
			for (const profileName of missingProfiles) {
				try {
					const profileConfig = ZHANLU_PROFILES.find((p) => p.name === profileName)
					if (!profileConfig) {
						outputChannel.appendLine(`Warning: Profile configuration not found for ${profileName}`)
						continue
					}

					if (hasCredentials) {
						// Create profile with credentials if user is logged in
						await provider.upsertProviderProfile(
							profileName,
							{
								apiProvider: "zhanlu" as any,
								zhanluAccessKey: accessKey,
								zhanluSecretKey: secretKey,
								zhanluToken: token,
								zhanluModelId: profileConfig.modelId,
							},
							false, // Don't activate immediately
						)
						outputChannel.appendLine(`Created Zhanlu profile with credentials: ${profileName}`)
					} else {
						// Create placeholder profile without credentials for future login
						await provider.upsertProviderProfile(
							profileName,
							{
								apiProvider: "zhanlu" as any,
								zhanluModelId: profileConfig.modelId,
								// Credentials will be filled during login
							},
							false, // Don't activate immediately
						)
						outputChannel.appendLine(`Created placeholder Zhanlu profile: ${profileName}`)
					}
					createdCount++
				} catch (error) {
					outputChannel.appendLine(`Warning: Failed to create profile ${profileName}: ${error}`)
				}
			}
		}

		// Reset to zhanlu if current profile was deprecated
		try {
			const currentState = await provider.getState()
			const currentProfileName = currentState.currentApiConfigName || ""

			if (deprecatedProfiles.some((p) => p.name === currentProfileName)) {
				// Check if 'zhanlu' profile exists, otherwise use 'default'
				const profileExists = existingProfileNames.includes("zhanlu") || missingProfiles.includes("zhanlu")
				const fallbackProfile = profileExists ? "zhanlu" : "default"

				await provider.activateProviderProfile({ name: fallbackProfile })
				outputChannel.appendLine(`Reset current API config to ${fallbackProfile}`)
			}
		} catch (error) {
			outputChannel.appendLine(`Warning: Failed to reset current profile: ${error}`)
		}

		// Mark sync as completed for current configuration
		await context.globalState.update(SYNC_KEY, true)

		// Clean up old sync keys to prevent state accumulation
		const allKeys = context.globalState.keys()
		const oldSyncKeys = allKeys.filter((key) => key.startsWith("zhanluProfileSync_") && key !== SYNC_KEY)
		for (const oldKey of oldSyncKeys) {
			await context.globalState.update(oldKey, undefined)
		}

		outputChannel.appendLine(
			`Zhanlu profile sync completed successfully. ` +
				`Removed ${removedCount} deprecated profiles, created ${createdCount} missing profiles. ` +
				`Current supported profiles: ${currentSupportedProfiles.join(", ")}`,
		)
	} catch (error) {
		outputChannel.appendLine(`Error during Zhanlu profile sync: ${error}`)
		// Don't mark as completed if there was an error, so it can retry next time
	}
}

// 将公共函数导出以供其他模块使用
export { applyZhanluMessageHandlers }
