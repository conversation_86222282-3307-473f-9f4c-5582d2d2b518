{"input": {"task_prompt": "Què ha de fer Z<PERSON>lu?", "task_placeholder": "Escriu la teva tasca aquí"}, "extension": {"name": "<PERSON><PERSON><PERSON>", "description": "Tot un equip de desenvolupadors d'IA al teu editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "Benvingut/da, {{name}}! Tens {{count}} notificacions.", "items": {"zero": "Cap element", "one": "Un element", "other": "{{count}} elements"}, "confirmation": {"reset_state": "Estàs segur que vols restablir tots els estats i emmagatzematge secret a l'extensió? Això no es pot desfer.", "delete_config_profile": "Estàs segur que vols eliminar aquest perfil de configuració?", "delete_custom_mode_with_rules": "Esteu segur que voleu suprimir aquest mode {scope}?\n\nAixò també suprimirà la carpeta de regles associada a:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Format d'URI de dades no vàlid", "error_copying_image": "Error copiant la imatge: {{errorMessage}}", "error_saving_image": "Error desant la imatge: {{errorMessage}}", "error_opening_image": "Error obrint la imatge: {{error}}", "could_not_open_file": "No s'ha pogut obrir el fitxer: {{errorMessage}}", "could_not_open_file_generic": "No s'ha pogut obrir el fitxer!", "checkpoint_timeout": "S'ha esgotat el temps en intentar restaurar el punt de control.", "checkpoint_failed": "Ha fallat la restauració del punt de control.", "git_not_installed": "Git és necessari per a la funció de punts de control. Si us plau, instal·la Git per activar els punts de control.", "no_workspace": "Si us plau, obre primer una carpeta de projecte", "update_support_prompt": "Ha fallat l'actualització del missatge de suport", "reset_support_prompt": "Ha fallat el restabliment del missatge de suport", "enhance_prompt": "Ha fallat la millora del missatge", "get_system_prompt": "Ha fallat l'obtenció del missatge del sistema", "search_commits": "Ha fallat la cerca de commits", "save_api_config": "Ha fallat el desament de la configuració de l'API", "create_api_config": "Ha fallat la creació de la configuració de l'API", "rename_api_config": "Ha fallat el canvi de nom de la configuració de l'API", "load_api_config": "Ha fallat la càrrega de la configuració de l'API", "delete_api_config": "Ha fallat l'eliminació de la configuració de l'API", "list_api_config": "Ha fallat l'obtenció de la llista de configuracions de l'API", "update_server_timeout": "Ha fallat l'actualització del temps d'espera del servidor", "failed_update_project_mcp": "Ha fallat l'actualització dels servidors MCP del projecte", "create_mcp_json": "Ha fallat la creació o obertura de .zhanlu/mcp.json: {{error}}", "hmr_not_running": "El servidor de desenvolupament local no està executant-se, l'HMR no funcionarà. Si us plau, executa 'npm run dev' abans de llançar l'extensió per habilitar l'HMR.", "retrieve_current_mode": "Error en recuperar el mode actual de l'estat.", "failed_delete_repo": "Ha fallat l'eliminació del repositori o branca associada: {{error}}", "failed_remove_directory": "Ha fallat l'eliminació del directori de tasques: {{error}}", "custom_storage_path_unusable": "La ruta d'emmagatzematge personalitzada \"{{path}}\" no és utilitzable, s'utilitzarà la ruta predeterminada", "cannot_access_path": "No es pot accedir a la ruta {{path}}: {{error}}", "settings_import_failed": "Ha fallat la importació de la configuració: {{error}}.", "mistake_limit_guidance": "Això pot indicar un error en el procés de pensament del model o la incapacitat d'utilitzar una eina correctament, que es pot mitigar amb orientació de l'usuari (p. ex. \"Prova de dividir la tasca en passos més petits\").", "violated_organization_allowlist": "Ha fallat l'execució de la tasca: el perfil actual no és compatible amb la configuració de la teva organització", "condense_failed": "Ha fallat la condensació del context", "condense_not_enough_messages": "No hi ha prou missatges per condensar el context", "condensed_recently": "El context s'ha condensat recentment; s'omet aquest intent", "condense_handler_invalid": "El gestor de l'API per condensar el context no és vàlid", "condense_context_grew": "La mida del context ha augmentat durant la condensació; s'omet aquest intent", "url_timeout": "El lloc web ha trigat massa a carregar (timeout). <PERSON><PERSON><PERSON> pot ser degut a una connexió lenta, un lloc web pesat o temporalment no disponible. Pots tornar-ho a provar més tard o comprovar si la URL és correcta.", "url_not_found": "No s'ha pogut trobar l'adreça del lloc web. Comprova si la URL és correcta i torna-ho a provar.", "no_internet": "No hi ha connexió a internet. Comprova la teva connexió de xarxa i torna-ho a provar.", "url_forbidden": "L'accés a aquest lloc web està prohibit. El lloc pot bloquejar l'accés automatitzat o requerir autenticació.", "url_page_not_found": "No s'ha trobat la pàgina. Comprova si la URL és correcta.", "url_fetch_failed": "Error en obtenir el contingut de la URL: {{error}}", "url_fetch_error_with_url": "Error en obtenir contingut per {{url}}: {{error}}", "command_timeout": "L'execució de la comanda ha superat el temps d'espera de {{seconds}} segons", "share_task_failed": "Ha fallat compartir la tasca. Si us plau, torna-ho a provar.", "share_no_active_task": "No hi ha cap tasca activa per compartir", "share_auth_required": "Es requereix autenticació. Si us plau, inicia sessió per compartir tasques.", "share_not_enabled": "La compartició de tasques no està habilitada per a aquesta organització.", "share_task_not_found": "Tasca no trobada o accés denegat.", "delete_rules_folder_failed": "Error en eliminar la carpeta de regles: {{rulesFolderPath}}. Error: {{error}}", "claudeCode": {"processExited": "El procés Claude Code ha sortit amb codi {{exitCode}}.", "errorOutput": "Sortida d'error: {{output}}", "processExitedWithError": "El procés Claude Code ha sortit amb codi {{exitCode}}. Sortida d'error: {{output}}", "stoppedWithReason": "<PERSON> s'ha aturat per la raó: {{reason}}", "apiKeyModelPlanMismatch": "Les claus API i els plans de subscripció permeten models diferents. Assegura't que el model seleccionat estigui inclòs al teu pla."}, "mode_import_failed": "Ha fallat la importació del mode: {{error}}"}, "warnings": {"no_terminal_content": "No s'ha seleccionat contingut de terminal", "missing_task_files": "Els fitxers d'aquesta tasca falten. Vols eliminar-la de la llista de tasques?", "auto_import_failed": "Ha fallat la importació automàtica de la configuració de RooCode: {{error}}"}, "info": {"no_changes": "No s'han trobat canvis.", "clipboard_copy": "Missatge del sistema copiat correctament al portapapers", "history_cleanup": "S'han netejat {{count}} tasques amb fitxers que falten de l'historial.", "custom_storage_path_set": "Ruta d'emmagatzematge personalitzada establerta: {{path}}", "default_storage_path": "S'ha reprès l'ús de la ruta d'emmagatzematge predeterminada", "settings_imported": "Configuració importada correctament.", "auto_import_success": "Configuració de RooCode importada automàticament des de {{filename}}", "share_link_copied": "Enllaç de compartició copiat al portapapers", "image_copied_to_clipboard": "URI de dades de la imatge copiada al portapapers", "image_saved": "Imatge desada a {{path}}", "organization_share_link_copied": "Enllaç de compartició d'organització copiat al porta-retalls!", "public_share_link_copied": "Enllaç de compartició pública copiat al porta-retalls!", "mode_exported": "Mode '{{mode}}' exportat correctament", "mode_imported": "Mode importat correctament"}, "answers": {"yes": "Sí", "no": "No", "remove": "Eliminar", "keep": "Mantenir"}, "buttons": {"save": "Desar", "edit": "<PERSON><PERSON>", "learn_more": "Més informació"}, "tasks": {"canceled": "Error de tasca: Ha estat aturada i cancel·lada per l'usuari.", "deleted": "Fallada de tasca: Ha estat aturada i eliminada per l'usuari.", "incomplete": "Tasca #{{taskNumber}} (Incompleta)", "no_messages": "Tasca #{{taskNumber}} (<PERSON> missatges)"}, "storage": {"prompt_custom_path": "Introdueix una ruta d'emmagatzematge personalitzada per a l'historial de converses o deixa-ho buit per utilitzar la ubicació predeterminada", "path_placeholder": "D:\\ZhanluStorage", "enter_absolute_path": "Introdueix una ruta completa (p. ex. D:\\ZhanluStorage o /home/<USER>/storage)", "enter_valid_path": "Introdueix una ruta vàlida"}, "settings": {"providers": {"groqApiKey": "Clau API de Groq", "getGroqApiKey": "Obté la clau API <PERSON>", "claudeCode": {"pathLabel": "<PERSON><PERSON>", "description": "Ruta opcional a la teva CLI de Claude Code. Per defecte 'claude' si no s'estableix.", "placeholder": "Per defecte: claude"}}}, "customModes": {"errors": {"yamlParseError": "YAML no vàlid al fitxer .zhanlumodes a la línia {{line}}. Comprova:\n• Indentació correcta (utilitza espais, no tabuladors)\n• Cometes i claudàtors coincidents\n• Sintaxi YAML vàlida", "schemaValidationError": "Format de modes personalitzats no vàlid a .<PERSON>:\n{{issues}}", "invalidFormat": "Format de modes personalitzats no vàlid. Assegura't que la teva configuració segueix el format YAML correcte.", "updateFailed": "Error en actualitzar el mode personalitzat: {{error}}", "deleteFailed": "Error en eliminar el mode personalitzat: {{error}}", "resetFailed": "Error en restablir els modes personalitzats: {{error}}", "modeNotFound": "Error d'escriptura: Mode no trobat", "noWorkspaceForProject": "No s'ha trobat cap carpeta d'espai de treball per al mode específic del projecte", "rulesCleanupFailed": "El mode s'ha suprimit correctament, però no s'ha pogut suprimir la carpeta de regles a {{rulesFolderPath}}. És possible que l'hagis de suprimir manualment."}, "scope": {"project": "projecte", "global": "global"}}, "marketplace": {"mode": {"rulesCleanupFailed": "El mode s'ha eliminat correctament, però no s'ha pogut eliminar la carpeta de regles a {{rulesFolderPath}}. És possible que l'hagis d'eliminar manualment."}}, "mdm": {"errors": {"cloud_auth_required": "La teva organització requereix autenticació de Roo Code Cloud. Si us plau, inicia sessió per continuar.", "organization_mismatch": "Has d'estar autenticat amb el compte de Roo Code Cloud de la teva organització.", "verification_failed": "No s'ha pogut verificar l'autenticació de l'organització."}}, "prompts": {"deleteMode": {"title": "Suprimeix el mode personalitzat", "description": "Esteu segur que voleu suprimir aquest mode {{scope}}? Això també suprimirà la carpeta de regles associada a: {{rulesFolderPath}}", "descriptionNoRules": "Esteu segur que voleu suprimir aquest mode personalitzat?", "confirm": "Suprimeix"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Evitar la finalització de tasques quan hi ha todos incomplets a la llista de todos"}}}