{"extension": {"name": "<PERSON><PERSON><PERSON>", "description": "Un intero team di sviluppatori AI nel tuo editor."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON><PERSON>, {{name}}! Hai {{count}} notifiche.", "items": {"zero": "<PERSON><PERSON><PERSON> elemento", "one": "Un elemento", "other": "{{count}} elementi"}, "confirmation": {"reset_state": "Sei sicuro di voler reimpostare tutti gli stati e l'archiviazione segreta nell'estensione? Questa azione non può essere annullata.", "delete_config_profile": "Sei sicuro di voler eliminare questo profilo di configurazione?", "delete_custom_mode_with_rules": "Sei sicuro di voler eliminare questa modalità {scope}?\n\nQuesto eliminerà anche la cartella delle regole associata in:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Formato URI dati non valido", "error_copying_image": "Errore durante la copia dell'immagine: {{errorMessage}}", "error_saving_image": "Errore durante il salvataggio dell'immagine: {{errorMessage}}", "error_opening_image": "Errore durante l'apertura dell'immagine: {{error}}", "could_not_open_file": "Impossibile aprire il file: {{errorMessage}}", "could_not_open_file_generic": "Impossibile aprire il file!", "checkpoint_timeout": "Timeout durante il tentativo di ripristinare il checkpoint.", "checkpoint_failed": "Impossibile ripristinare il checkpoint.", "git_not_installed": "Git è richiesto per la funzione di checkpoint. Per favore, installa Git per abilitare i checkpoint.", "no_workspace": "Per favore, apri prima una cartella di progetto", "update_support_prompt": "Errore durante l'aggiornamento del messaggio di supporto", "reset_support_prompt": "Errore durante il ripristino del messaggio di supporto", "enhance_prompt": "Errore durante il miglioramento del messaggio", "get_system_prompt": "Errore durante l'ottenimento del messaggio di sistema", "search_commits": "Errore durante la ricerca dei commit", "save_api_config": "Errore durante il salvataggio della configurazione API", "create_api_config": "Errore durante la creazione della configurazione API", "rename_api_config": "Errore durante la ridenominazione della configurazione API", "load_api_config": "Errore durante il caricamento della configurazione API", "delete_api_config": "Errore durante l'eliminazione della configurazione API", "list_api_config": "Errore durante l'ottenimento dell'elenco delle configurazioni API", "update_server_timeout": "Errore durante l'aggiornamento del timeout del server", "failed_update_project_mcp": "Errore durante l'aggiornamento dei server MCP del progetto", "create_mcp_json": "Impossibile creare o aprire .zhanlu/mcp.json: {{error}}", "hmr_not_running": "Il server di sviluppo locale non è in esecuzione, l'HMR non funzionerà. Esegui 'npm run dev' prima di avviare l'estensione per abilitare l'HMR.", "retrieve_current_mode": "Errore durante il recupero della modalità corrente dallo stato.", "failed_delete_repo": "Impossibile eliminare il repository o il ramo associato: {{error}}", "failed_remove_directory": "Impossibile rimuovere la directory delle attività: {{error}}", "custom_storage_path_unusable": "Il percorso di archiviazione personalizzato \"{{path}}\" non è utilizzabile, verrà utilizzato il percorso predefinito", "cannot_access_path": "Impossibile accedere al percorso {{path}}: {{error}}", "settings_import_failed": "Importazione delle impostazioni fallita: {{error}}.", "mistake_limit_guidance": "Questo può indicare un fallimento nel processo di pensiero del modello o l'incapacità di utilizzare correttamente uno strumento, che può essere mitigato con la guida dell'utente (ad es. \"Prova a suddividere l'attività in passaggi più piccoli\").", "violated_organization_allowlist": "Impossibile eseguire l'attività: il profilo corrente non è compatibile con le impostazioni della tua organizzazione", "condense_failed": "Impossibile condensare il contesto", "condense_not_enough_messages": "Non ci sono abbastanza messaggi per condensare il contesto", "condensed_recently": "Il contesto è stato condensato di recente; questo tentativo viene saltato", "condense_handler_invalid": "Il gestore API per condensare il contesto non è valido", "condense_context_grew": "La dimensione del contesto è aumentata durante la condensazione; questo tentativo viene saltato", "url_timeout": "Il sito web ha impiegato troppo tempo a caricarsi (timeout). <PERSON><PERSON> potrebbe essere dovuto a una connessione lenta, un sito web pesante o temporaneamente non disponibile. Puoi riprovare più tardi o verificare se l'URL è corretto.", "url_not_found": "L'indirizzo del sito web non è stato trovato. Verifica se l'URL è corretto e riprova.", "no_internet": "Nessuna connessione internet. Verifica la tua connessione di rete e riprova.", "url_forbidden": "L'accesso a questo sito web è vietato. Il sito potrebbe bloccare l'accesso automatizzato o richiedere autenticazione.", "url_page_not_found": "La pagina non è stata trovata. Verifica se l'URL è corretto.", "url_fetch_failed": "Errore nel recupero del contenuto URL: {{error}}", "url_fetch_error_with_url": "Errore nel recupero del contenuto per {{url}}: {{error}}", "command_timeout": "Esecuzione del comando scaduta dopo {{seconds}} secondi", "share_task_failed": "Condivisione dell'attività fallita. Riprova.", "share_no_active_task": "Nessuna attività attiva da condividere", "share_auth_required": "Autenticazione richiesta. Accedi per condividere le attività.", "share_not_enabled": "La condivisione delle attività non è abilitata per questa organizzazione.", "share_task_not_found": "Attività non trovata o accesso negato.", "mode_import_failed": "Importazione della modalità non riuscita: {{error}}", "delete_rules_folder_failed": "Impossibile eliminare la cartella delle regole: {{rulesFolderPath}}. Errore: {{error}}", "claudeCode": {"processExited": "Il processo Claude Code è terminato con codice {{exitCode}}.", "errorOutput": "Output di errore: {{output}}", "processExitedWithError": "Il processo Claude Code è terminato con codice {{exitCode}}. Output di errore: {{output}}", "stoppedWithReason": "<PERSON> Code si è fermato per il motivo: {{reason}}", "apiKeyModelPlanMismatch": "Le chiavi API e i piani di abbonamento consentono modelli diversi. Assicurati che il modello selezionato sia incluso nel tuo piano."}}, "warnings": {"no_terminal_content": "Nessun contenuto del terminale selezionato", "missing_task_files": "I file di questa attività sono mancanti. Vuoi rimuoverla dall'elenco delle attività?", "auto_import_failed": "Importazione automatica delle impostazioni RooCode fallita: {{error}}"}, "info": {"no_changes": "Nessuna modifica trovata.", "clipboard_copy": "Messaggio di sistema copiato con successo negli appunti", "history_cleanup": "Pulite {{count}} attività con file mancanti dalla cronologia.", "custom_storage_path_set": "Percorso di archiviazione personalizzato impostato: {{path}}", "default_storage_path": "Tornato al percorso di archiviazione predefinito", "settings_imported": "Impostazioni importate con successo.", "auto_import_success": "Impostazioni RooCode importate automaticamente da {{filename}}", "share_link_copied": "Link di condivisione copiato negli appunti", "image_copied_to_clipboard": "URI dati dell'immagine copiato negli appunti", "image_saved": "Immagine salvata in {{path}}", "organization_share_link_copied": "Link di condivisione organizzazione copiato negli appunti!", "public_share_link_copied": "Link di condivisione pubblica copiato negli appunti!", "mode_exported": "Modalità '{{mode}}' esportata con successo", "mode_imported": "Modalità importata con successo"}, "answers": {"yes": "Sì", "no": "No", "remove": "<PERSON><PERSON><PERSON><PERSON>", "keep": "<PERSON><PERSON><PERSON>"}, "buttons": {"save": "<PERSON><PERSON>", "edit": "Modifica", "learn_more": "Scopri di più"}, "tasks": {"canceled": "Errore attività: È stata interrotta e annullata dall'utente.", "deleted": "Fallimento attività: È stata interrotta ed eliminata dall'utente.", "incomplete": "Attività #{{taskNumber}} (Incompleta)", "no_messages": "Attività #{{taskNumber}} (<PERSON><PERSON><PERSON> messag<PERSON>)"}, "storage": {"prompt_custom_path": "Inserisci il percorso di archiviazione personalizzato per la cronologia delle conversazioni, lascia vuoto per utilizzare la posizione predefinita", "path_placeholder": "D:\\ZhanluStorage", "enter_absolute_path": "Inserisci un percorso assoluto (ad esempio D:\\ZhanluStorage o /home/<USER>/storage)", "enter_valid_path": "Inserisci un percorso valido"}, "input": {"task_prompt": "Cosa deve fare <PERSON>lu?", "task_placeholder": "Scrivi il tuo compito qui"}, "settings": {"providers": {"groqApiKey": "Chiave API Groq", "getGroqApiKey": "Ottieni chiave API Groq", "claudeCode": {"pathLabel": "Percorso Claude Code", "description": "Percorso opzionale alla tua CLI Claude Code. Predefinito 'claude' se non impostato.", "placeholder": "Predefinito: claude"}}}, "customModes": {"errors": {"yamlParseError": "YAML non valido nel file .zhanlumodes alla riga {{line}}. Controlla:\n• Indentazione corretta (usa spazi, non tab)\n• Virgolette e parentesi corrispondenti\n• Sintassi YAML valida", "schemaValidationError": "Formato modalità personalizzate non valido in .zhanlumodes:\n{{issues}}", "invalidFormat": "Formato modalità personalizzate non valido. Assicurati che le tue impostazioni seguano il formato YAML corretto.", "updateFailed": "Aggiornamento modalità personalizzata fallito: {{error}}", "deleteFailed": "Eliminazione modalità personalizzata fallita: {{error}}", "resetFailed": "Reset modalità personalizzate fallito: {{error}}", "modeNotFound": "Errore di scrittura: Modalità non trovata", "noWorkspaceForProject": "Nessuna cartella workspace trovata per la modalità specifica del progetto", "rulesCleanupFailed": "La modalità è stata eliminata con successo, ma non è stato possibile eliminare la cartella delle regole in {{rulesFolderPath}}. Potrebbe essere necessario eliminarla manualmente."}, "scope": {"project": "progetto", "global": "globale"}}, "marketplace": {"mode": {"rulesCleanupFailed": "La modalità è stata rimossa con successo, ma non è stato possibile eliminare la cartella delle regole in {{rulesFolderPath}}. Potrebbe essere necessario eliminarla manualmente."}}, "mdm": {"errors": {"cloud_auth_required": "La tua organizzazione richiede l'autenticazione Roo Code Cloud. Accedi per continuare.", "organization_mismatch": "Devi essere autenticato con l'account Roo Code Cloud della tua organizzazione.", "verification_failed": "Impossibile verificare l'autenticazione dell'organizzazione."}}, "prompts": {"deleteMode": {"title": "Elimina Modalità Personalizzata", "description": "Sei sicuro di voler eliminare questa modalità {{scope}}? Questo eliminerà anche la cartella delle regole associata a: {{rulesFolderPath}}", "descriptionNoRules": "Sei sicuro di voler eliminare questa modalità personalizzata?", "confirm": "Elimina"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Impedire il completamento delle attività quando ci sono todo incompleti nella lista dei todo"}}}