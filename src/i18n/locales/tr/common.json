{"extension": {"name": "<PERSON><PERSON><PERSON>", "description": "Düzenleyicinizde tam bir AI geliştirici ekibi."}, "number_format": {"thousand_suffix": "k", "million_suffix": "m", "billion_suffix": "b"}, "welcome": "<PERSON><PERSON> geldiniz, {{name}}! {{count}} bild<PERSON><PERSON>z var.", "items": {"zero": "<PERSON><PERSON><PERSON> yok", "one": "B<PERSON>", "other": "{{count}} <PERSON><PERSON><PERSON>"}, "confirmation": {"reset_state": "Uzantıdaki tüm durumları ve gizli depolamayı sıfırlamak istediğinizden emin misiniz? Bu işlem geri alınamaz.", "delete_config_profile": "Bu yapılandırma profilini silmek istediğinizden emin misiniz?", "delete_custom_mode_with_rules": "Bu {scope} modunu silmek istediğinizden emin misiniz?\n\nBu <PERSON>, iliş<PERSON>li kurallar klasörünü de şu konumdan silecektir:\n{rulesFolderPath}"}, "errors": {"invalid_data_uri": "Geçersiz veri URI formatı", "error_copying_image": "<PERSON><PERSON><PERSON> k<PERSON> hata oluştu: {{errorMessage}}", "error_saving_image": "<PERSON><PERSON><PERSON> ka<PERSON>en hata o<PERSON>: {{errorMessage}}", "error_opening_image": "<PERSON><PERSON><PERSON> a<PERSON>ılırken hata oluştu: {{error}}", "could_not_open_file": "<PERSON><PERSON><PERSON> a<PERSON>ı<PERSON>adı: {{errorMessage}}", "could_not_open_file_generic": "Dosya açılamadı!", "checkpoint_timeout": "Kontrol noktasını geri yüklemeye çalışırken zaman aşımına uğradı.", "checkpoint_failed": "Kontrol noktası geri <PERSON>i.", "git_not_installed": "Kontrol noktaları özelliği için Git gereklidir. Kontrol noktalarını etkinleştirmek için lütfen Git'i yükleyin.", "no_workspace": "Lütfen önce bir proje klasörü açın", "update_support_prompt": "Destek istemi gü<PERSON>llenemedi", "reset_support_prompt": "Destek istemi sıfırl<PERSON>madı", "enhance_prompt": "İstem geliştirilemedi", "get_system_prompt": "Sistem istemi alı<PERSON>ı", "search_commits": "Taahhütler aranamadı", "save_api_config": "API yapılandırması kaydedilemedi", "create_api_config": "API yapılandırması oluşturulamadı", "rename_api_config": "API yapılandırmasının adı değiştirilemedi", "load_api_config": "API yapılandırması yüklenemedi", "delete_api_config": "API yapılandırması silinemedi", "list_api_config": "API yapılandırma listesi alınamadı", "update_server_timeout": "<PERSON><PERSON><PERSON> zaman aşımı güncellenemedi", "failed_update_project_mcp": "Proje MCP sunucuları güncellenemedi", "create_mcp_json": ".zhanlu/mcp.json oluşturulamadı veya açılamadı: {{error}}", "hmr_not_running": "<PERSON><PERSON><PERSON> sun<PERSON>, HMR çalışmayacak. HMR'yi etkinleştirmek için uzantıyı başlatmadan önce lütfen 'npm run dev' komutunu çalıştırın.", "retrieve_current_mode": "Mevcut mod durumdan alı<PERSON>ı<PERSON>en hata oluştu.", "failed_delete_repo": "İlişkili gölge depo veya dal silinemedi: {{error}}", "failed_remove_directory": "G<PERSON><PERSON>v dizini kaldı<PERSON>ılamadı: {{error}}", "custom_storage_path_unusable": "<PERSON><PERSON> depolama yolu \"{{path}}\" k<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, var<PERSON><PERSON><PERSON> yol kullanılacak", "cannot_access_path": "{{path}} yo<PERSON><PERSON>: {{error}}", "settings_import_failed": "Ayarlar içe aktarılamadı: {{error}}.", "mistake_limit_guidance": "<PERSON><PERSON>, <PERSON><PERSON> dü<PERSON><PERSON>nce sürecindeki bir başarısızlığı veya bir aracı düzgün kullanamama durumunu gö<PERSON>ebilir, bu da kullanıcı rehberliği ile hafifletilebilir (örn. \"Görevi daha küçük adımlara bölmeyi deneyin\").", "violated_organization_allowlist": "Görev yürütülemedi: Geçerli profil kuruluşunuzun ayarlarıyla uyumlu değil", "condense_failed": "Bağlam sıkıştırılamadı", "condense_not_enough_messages": "Bağlamı sıkıştırmak için yeterli mesaj yok", "condensed_recently": "Bağlam yakın zamanda sıkıştırıldı; bu deneme atlanıyor", "condense_handler_invalid": "Bağlamı sıkıştırmak için API işleyicisi geçersiz", "condense_context_grew": "Sık<PERSON><PERSON><PERSON><PERSON><PERSON> sı<PERSON>ında bağlam boyutu arttı; bu deneme atlanıyor", "url_timeout": "Web sitesi yüklenmesi çok uzun sürdü (zaman aşımı). Bu yavaş bağlantı, ağır web sitesi veya geçici olarak kullanılamama nedeniyle olabilir. Daha sonra tekrar deneyebilir veya URL'nin doğru olup olmadığını kontrol edebilirsin.", "url_not_found": "Web sitesi adresi bulunamadı. URL'nin doğru olup olmadığını kontrol et ve tekrar dene.", "no_internet": "İnternet bağlantısı yok. Ağ bağlantını kontrol et ve tekrar dene.", "url_forbidden": "Bu web sitesine erişim yasak. Site otomatik erişimi engelliyor veya kimlik doğrulama gerektiriyor olabilir.", "url_page_not_found": "Sayfa bulunamadı. URL'nin doğru olup olmadığını kontrol et.", "url_fetch_failed": "URL içeriği getirme hatası: {{error}}", "url_fetch_error_with_url": "{{url}} i<PERSON><PERSON> içerik getirme hatası: {{error}}", "command_timeout": "<PERSON><PERSON><PERSON>ıştırma {{seconds}} saniye sonra zaman aşımına uğradı", "share_task_failed": "Görev paylaşılamadı", "share_no_active_task": "Paylaşılacak aktif görev yok", "share_auth_required": "Kimlik doğrulama gerekli. Görevleri paylaşmak için lütfen giriş yapın.", "share_not_enabled": "Bu kuruluş için görev paylaşımı etkinleştirilmemiş.", "share_task_not_found": "Görev bulunamadı veya erişim reddedildi.", "mode_import_failed": "Mod içe aktarılamadı: {{error}}", "delete_rules_folder_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> silinemedi: {{rulesFolderPath}}. Hata: {{error}}", "claudeCode": {"processExited": "Claude Code işlemi {{exitCode}} koduyla çıktı.", "errorOutput": "Hata çıktısı: {{output}}", "processExitedWithError": "Claude Code işlemi {{exitCode}} kod<PERSON>la çıktı. Hata çıktısı: {{output}}", "stoppedWithReason": "<PERSON> şu nedenle durdu: {{reason}}", "apiKeyModelPlanMismatch": "API anahtarları ve abonelik planları farklı modellere izin verir. Seçilen modelin planınıza dahil olduğundan emin olun."}}, "warnings": {"no_terminal_content": "Seçili terminal içeriği yok", "missing_task_files": "Bu görevin dosyaları eksik. Görev listesinden kaldırmak istiyor musunuz?", "auto_import_failed": "RooCode ayarları otomatik olarak içe aktarılamadı: {{error}}"}, "info": {"no_changes": "Değişiklik bulunamadı.", "clipboard_copy": "Sistem istemi panoya başarıyla kopyalandı", "history_cleanup": "Geçmişten eksik dosyaları olan {{count}} görev temizlendi.", "custom_storage_path_set": "<PERSON><PERSON> depolama yolu ayarlandı: {{path}}", "default_storage_path": "Varsayılan depolama yoluna geri <PERSON>", "settings_imported": "Ayarlar başarıyla içe aktarıldı.", "auto_import_success": "RooCode ayarları {{filename}} dosyasından otomatik olarak içe aktarıldı", "share_link_copied": "Paylaşım bağlantısı panoya kopyalandı", "image_copied_to_clipboard": "Resim veri URI'si panoya kopyalandı", "image_saved": "Resim {{path}} kon<PERSON><PERSON> kaydedildi", "organization_share_link_copied": "Kuruluş paylaşım bağlantısı panoya kopyalandı!", "public_share_link_copied": "Herkese açık paylaşım bağlantısı panoya kopyalandı!", "mode_exported": "'{{mode}}' modu ba<PERSON><PERSON><PERSON><PERSON> dışa aktarıldı", "mode_imported": "Mod başarıyla içe aktarıldı"}, "answers": {"yes": "<PERSON><PERSON>", "no": "Hay<PERSON><PERSON>", "remove": "Kaldır", "keep": "<PERSON><PERSON>"}, "buttons": {"save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "learn_more": "<PERSON><PERSON>"}, "tasks": {"canceled": "Görev hatası: Kullanıcı tarafından durduruldu ve iptal edildi.", "deleted": "Görev başarısız: Kullanıcı tarafından durduruldu ve silindi.", "incomplete": "Görev #{{taskNumber}} (Tamamlanmamış)", "no_messages": "Görev #{{taskNumber}} (<PERSON>j yok)"}, "storage": {"prompt_custom_path": "Konuşma geçmişi için özel depolama yolunu girin, var<PERSON><PERSON><PERSON> konumu kullanmak için boş bırakın", "path_placeholder": "D:\\ZhanluStorage", "enter_absolute_path": "Lütfen mutlak bir yol girin (örn. D:\\ZhanluStorage veya /home/<USER>/storage)", "enter_valid_path": "Lütfen geçerli bir yol girin"}, "input": {"task_prompt": "<PERSON><PERSON><PERSON> ne yapsın?", "task_placeholder": "<PERSON><PERSON><PERSON><PERSON>i buraya yaz"}, "settings": {"providers": {"groqApiKey": "Groq API Anahtarı", "getGroqApiKey": "Groq API Anahtarı Al", "claudeCode": {"pathLabel": "<PERSON>", "description": "Claude Code CLI'nizin isteğe bağlı yolu. Ayarlanmazsa var<PERSON>ılan olarak 'claude' olur.", "placeholder": "Varsayılan: claude"}}}, "customModes": {"errors": {"yamlParseError": ".zhanlumodes dosyasının {{line}}. satırında geçersiz YAML. Kontrol et:\n• <PERSON><PERSON><PERSON> giri<PERSON> (tab değil boş<PERSON> kullan)\n• Eşleşen tırnak işaretleri ve parantezler\n• Geçerli YAML sözdizimi", "schemaValidationError": ".zhanlumodes'ta geçersiz özel mod formatı:\n{{issues}}", "invalidFormat": "Geçersiz özel mod formatı. Ayarlarının doğru YAML formatını takip ettiğinden emin ol.", "updateFailed": "Özel mod güncellemesi başarısız: {{error}}", "deleteFailed": "Özel mod silme başar<PERSON>sız: {{error}}", "resetFailed": "<PERSON>zel modları sıfırlama başarısız: {{error}}", "modeNotFound": "Yazma hatası: <PERSON><PERSON> bulu<PERSON>adı", "noWorkspaceForProject": "Proje özel modu için ç<PERSON>ı<PERSON> alanı klasörü bulunamadı", "rulesCleanupFailed": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>, anca<PERSON> {{rulesFolderPath}} konumundaki kurallar klasörü silinemedi. <PERSON> olarak silmeniz gerekebilir."}, "scope": {"project": "proje", "global": "<PERSON><PERSON><PERSON><PERSON>"}}, "marketplace": {"mode": {"rulesCleanupFailed": "<PERSON>d b<PERSON><PERSON><PERSON><PERSON><PERSON> kald<PERSON>, ancak {{rulesFolderPath}} konumundaki kurallar klasörü silinemedi. <PERSON> olarak silmeniz gerekebilir."}}, "mdm": {"errors": {"cloud_auth_required": "Kuruluşunuz Roo Code Cloud kimlik doğrulaması gerektiriyor. Devam etmek için giriş yapın.", "organization_mismatch": "Kuruluşunuzun Roo Code Cloud hesabıyla kimlik doğrulaması yapmalısınız.", "verification_failed": "Kuruluş kimlik doğrulaması doğrulanamıyor."}}, "prompts": {"deleteMode": {"title": "<PERSON><PERSON>", "description": "Bu {{scope}} modunu silmek istediğinizden emin misiniz? Bu, {{rulesFolderPath}} adresindeki ilişkili kurallar klasörünü de silecektir", "descriptionNoRules": "Bu özel modu silmek istediğinizden emin misiniz?", "confirm": "Sil"}}, "commands": {"preventCompletionWithOpenTodos": {"description": "Todo listesinde tamamlanmamış todolar olduğunda görev tamamlanmasını engelle"}}}